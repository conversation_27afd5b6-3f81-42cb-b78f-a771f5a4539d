### hrmscript
#### Description
OrangeHRM is a comprehensive Human Resource Management System tailored for small to medium-sized businesses operating within a single country. It offers a full-service HR solution, including modules for employee management, time tracking, payroll, and performance.
This script — ohrmscript — facilitates a critical integration between biometric attendance systems and the OrangeHRM platform. It enables HR teams to automate the synchronization of employee attendance data by bridging the SQL database used by biometric devices with the MySQL database that powers OrangeHRM.

#### Main functionalities
1. Automated Attendance sync
   > Transfer punch in and punch out data from SQL to MYSQL
3. SQL to MYSQL Translation
   > Bridges data from external biometric databases to orangehrm schema
5. Data Validation
   > Ensures record accuracy before insertion.
7. Scheduled updates
   > The script fetch the data from sql database every 5 minutes.
9. Reporting-Ready Integration
   > The HR team can view the records of each employees as 

#### Installation Guide
1. Clone the repository
   ```bash
   git clone https://github.com/ourtelekom/hrmscript.git
   cd hrmscript
   ```

2. Set up the environment
   * Python version: 3.13.3 or higher
   * Create and activate a virtual environment:
     ```bash
     python -m venv venv
     source venv/bin/activate  # On Windows: venv\Scripts\activate
     ```
   * Install required packages:
     ```bash
     pip install -r requirements.txt
     ```

3. Configure environment variables
   * Copy the example environment file:
     ```bash
     cp .env.example .env
     ```
   * Edit the `.env` file with your actual database credentials:
     ```bash
     nano .env  # or use your preferred editor
     ```
   * **Important**: Never commit the `.env` file to version control as it contains sensitive information.

4. Required Environment Variables
   The following environment variables must be set in your `.env` file:

   **SQL Server (Source Database):**
   - `SOURCE_DB_SERVER` - SQL Server instance name
   - `SOURCE_DB_DATABASE` - Database name
   - `SOURCE_DB_USERNAME` - Username for SQL Server
   - `SOURCE_DB_PASSWORD` - Password for SQL Server

   **OrangeHRM MySQL (Destination Database):**
   - `ORANGEHRM_DB_HOST` - MySQL host address
   - `ORANGEHRM_DB_USER` - MySQL username
   - `ORANGEHRM_DB_PASSWORD` - MySQL password
   - `ORANGEHRM_DB_DATABASE` - MySQL database name

   **Optional Configuration:**
   - `DEFAULT_TIMEZONE` - Timezone for attendance records (default: Pacific/Guadalcanal)
   - `LOG_LEVEL` - Logging level (default: INFO)
   - `LOG_FILE` - Log file name (default: attendance_transfer.log)

5. Run the script
   ```bash
   python sqlscript.py
   ```

6. **Testing (Recommended)**
   Run the test suite to verify everything works correctly:
   ```bash
   # Install test dependencies
   pip install pytest pytest-mock pytest-cov

   # Run basic functionality tests
   python -m pytest tests/test_basic_functionality.py -v

   # Run all tests
   python -m pytest -v

   # Run tests with coverage
   python -m pytest --cov=sqlscript --cov-report=term-missing
   ```

   See [TESTING.md](TESTING.md) for detailed testing information.

Yes! You are now ready to run the script securely with environment variables.

#### Security Best Practices
1. **Environment Variables**: All sensitive information (passwords, connection strings) is now stored in environment variables
2. **File Security**: The `.env` file is automatically excluded from version control via `.gitignore`
3. **Credential Management**: Never hardcode credentials in source code
4. **Access Control**: Ensure database users have minimal required permissions
5. **Regular Updates**: Keep dependencies updated for security patches

##### Troubleshooting Steps
1. **Check database connections**
   - Verify environment variables are correctly set in `.env` file
   - Test database connectivity manually
   - Check firewall and network settings

2. **Environment Variable Issues**
   - Ensure `.env` file exists and is in the same directory as the script
   - Verify all required variables are set (see error messages for missing variables)
   - Check for typos in variable names

3. **Permission Issues**
   - Verify database user permissions for both source and destination databases
   - Ensure the script has read access to the `.env` file
