"""
Basic functionality tests for the attendance transfer script.
These tests focus on core functions without complex mocking.
"""
import pytest
import os
import sys
from unittest.mock import patch
from datetime import datetime
import pytz

# Add the parent directory to the path to import sqlscript
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlscript


class TestBasicFunctionality:
    """Test basic functionality without complex database mocking."""
    
    def test_check_required_env_vars_with_all_vars(self):
        """Test environment variable checking with all required vars present."""
        required_env = {
            'SOURCE_DB_SERVER': 'test-server',
            'SOURCE_DB_DATABASE': 'test_db',
            'SOURCE_DB_USERNAME': 'test_user',
            'SOURCE_DB_PASSWORD': 'test_pass',
            'ORANGEHRM_DB_HOST': 'test-host',
            'ORANGEHRM_DB_USER': 'test_mysql_user',
            'ORANGEHRM_DB_PASSWORD': 'test_mysql_pass',
            'ORANGEHRM_DB_DATABASE': 'test_mysql_db'
        }
        
        with patch.dict(os.environ, required_env, clear=True):
            result = sqlscript.check_required_env_vars()
            assert result is True
    
    def test_check_required_env_vars_missing_some(self):
        """Test environment variable checking with missing vars."""
        incomplete_env = {
            'SOURCE_DB_SERVER': 'test-server',
            'SOURCE_DB_DATABASE': 'test_db',
            # Missing other required vars
        }
        
        with patch.dict(os.environ, incomplete_env, clear=True):
            result = sqlscript.check_required_env_vars()
            assert result is False
    
    def test_convert_to_utc_and_offset_pacific_guadalcanal(self):
        """Test time conversion for Pacific/Guadalcanal timezone."""
        naive_dt = datetime(2025, 7, 28, 10, 30, 0)
        
        with patch('sqlscript.DEFAULT_TIMEZONE', 'Pacific/Guadalcanal'):
            utc_dt, offset = sqlscript.convert_to_utc_and_offset(naive_dt)
            
            # Pacific/Guadalcanal is UTC+11
            assert offset == 660  # 11 hours * 60 minutes
            assert isinstance(utc_dt, datetime)
            assert utc_dt.tzinfo is None  # Should be naive UTC
    
    def test_convert_to_utc_and_offset_utc(self):
        """Test time conversion for UTC timezone."""
        naive_dt = datetime(2025, 7, 28, 10, 30, 0)
        
        with patch('sqlscript.DEFAULT_TIMEZONE', 'UTC'):
            utc_dt, offset = sqlscript.convert_to_utc_and_offset(naive_dt)
            
            assert offset == 0  # UTC has no offset
            assert utc_dt == naive_dt  # Should be the same time
    
    def test_convert_to_utc_and_offset_invalid_timezone(self, caplog):
        """Test time conversion with invalid timezone."""
        naive_dt = datetime(2025, 7, 28, 10, 30, 0)
        
        with patch('sqlscript.DEFAULT_TIMEZONE', 'Invalid/Timezone'):
            utc_dt, offset = sqlscript.convert_to_utc_and_offset(naive_dt)
            
            # Should fallback to original datetime and 0 offset
            assert utc_dt == naive_dt
            assert offset == 0
            assert "Invalid default timezone" in caplog.text
    
    def test_convert_to_utc_and_offset_with_timezone_aware_datetime(self):
        """Test time conversion with timezone-aware datetime input."""
        # Create a timezone-aware datetime
        tz = pytz.timezone('Pacific/Guadalcanal')
        aware_dt = tz.localize(datetime(2025, 7, 28, 10, 30, 0))
        
        with patch('sqlscript.DEFAULT_TIMEZONE', 'Pacific/Guadalcanal'):
            utc_dt, offset = sqlscript.convert_to_utc_and_offset(aware_dt)
            
            assert offset == 660  # Pacific/Guadalcanal is UTC+11
            assert isinstance(utc_dt, datetime)
            assert utc_dt.tzinfo is None  # Should be naive UTC
    
    def test_convert_to_utc_and_offset_error_handling(self):
        """Test time conversion error handling."""
        # Test with None input (should cause an error)
        with patch('sqlscript.DEFAULT_TIMEZONE', 'UTC'):
            try:
                utc_dt, offset = sqlscript.convert_to_utc_and_offset(None)
                # If it doesn't raise an exception, check fallback behavior
                assert offset == 0
            except Exception:
                # Exception is expected for None input
                pass


class TestConfigurationLoading:
    """Test configuration loading from environment variables."""
    
    def test_source_db_config_loading(self):
        """Test that SOURCE_DB_CONFIG uses environment variables."""
        test_env = {
            'SOURCE_DB_DRIVER': 'Test Driver',
            'SOURCE_DB_SERVER': 'test-server',
            'SOURCE_DB_DATABASE': 'test_db',
            'SOURCE_DB_USERNAME': 'test_user',
            'SOURCE_DB_PASSWORD': 'test_pass',
            'SOURCE_DB_TRUSTED_CONNECTION': 'no',
            'SOURCE_DB_ENCRYPT': 'true'
        }
        
        with patch.dict(os.environ, test_env):
            # Reload module to pick up new environment variables
            import importlib
            importlib.reload(sqlscript)
            
            config = sqlscript.SOURCE_DB_CONFIG
            assert config['Driver'] == 'Test Driver'
            assert config['server'] == 'test-server'
            assert config['database'] == 'test_db'
            assert config['username'] == 'test_user'
            assert config['password'] == 'test_pass'
            assert config['trusted_connetion'] == 'no'  # Note: typo in original code
            assert config['encrypt'] == 'true'
    
    def test_orangehrm_db_config_loading(self):
        """Test that ORANGEHRM_DB_CONFIG uses environment variables."""
        test_env = {
            'ORANGEHRM_DB_HOST': 'test-mysql-host',
            'ORANGEHRM_DB_PORT': '3307',
            'ORANGEHRM_DB_USER': 'test_mysql_user',
            'ORANGEHRM_DB_PASSWORD': 'test_mysql_pass',
            'ORANGEHRM_DB_DATABASE': 'test_mysql_db'
        }
        
        with patch.dict(os.environ, test_env):
            import importlib
            importlib.reload(sqlscript)
            
            config = sqlscript.ORANGEHRM_DB_CONFIG
            assert config['host'] == 'test-mysql-host'
            assert config['port'] == 3307  # Should be converted to int
            assert config['user'] == 'test_mysql_user'
            assert config['password'] == 'test_mysql_pass'
            assert config['database'] == 'test_mysql_db'
    
    def test_default_timezone_loading(self):
        """Test that DEFAULT_TIMEZONE uses environment variable."""
        test_env = {'DEFAULT_TIMEZONE': 'America/New_York'}
        
        with patch.dict(os.environ, test_env):
            import importlib
            importlib.reload(sqlscript)
            
            assert sqlscript.DEFAULT_TIMEZONE == 'America/New_York'
    
    def test_config_defaults(self):
        """Test that configuration has sensible defaults."""
        # Clear all relevant environment variables
        env_vars_to_clear = [
            'SOURCE_DB_DRIVER', 'ORANGEHRM_DB_PORT', 'DEFAULT_TIMEZONE'
        ]
        
        with patch.dict(os.environ, {}, clear=False):
            # Remove specific vars
            for var in env_vars_to_clear:
                if var in os.environ:
                    del os.environ[var]
            
            import importlib
            importlib.reload(sqlscript)
            
            # Check defaults
            assert sqlscript.SOURCE_DB_CONFIG['Driver'] == 'ODBC Driver 17 for SQL Server'
            assert sqlscript.ORANGEHRM_DB_CONFIG['port'] == 3306
            assert sqlscript.DEFAULT_TIMEZONE == 'Pacific/Guadalcanal'


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_module_imports(self):
        """Test that all required modules can be imported."""
        # This is a basic smoke test
        assert hasattr(sqlscript, 'schedule')
        assert hasattr(sqlscript, 'time')
        assert hasattr(sqlscript, 'mysql')
        assert hasattr(sqlscript, 'pytz')
        assert hasattr(sqlscript, 'logging')
        assert hasattr(sqlscript, 'pyodbc')
        assert hasattr(sqlscript, 'os')
    
    def test_required_functions_exist(self):
        """Test that all required functions exist."""
        required_functions = [
            'check_required_env_vars',
            'create_source_connection',
            'create_orangehrm_connection',
            'get_attendance_records',
            'convert_to_utc_and_offset',
            'transfer_attendance_data'
        ]
        
        for func_name in required_functions:
            assert hasattr(sqlscript, func_name)
            assert callable(getattr(sqlscript, func_name))
    
    def test_configuration_objects_exist(self):
        """Test that configuration objects exist."""
        assert hasattr(sqlscript, 'SOURCE_DB_CONFIG')
        assert hasattr(sqlscript, 'ORANGEHRM_DB_CONFIG')
        assert hasattr(sqlscript, 'DEFAULT_TIMEZONE')
        
        assert isinstance(sqlscript.SOURCE_DB_CONFIG, dict)
        assert isinstance(sqlscript.ORANGEHRM_DB_CONFIG, dict)
        assert isinstance(sqlscript.DEFAULT_TIMEZONE, str)
