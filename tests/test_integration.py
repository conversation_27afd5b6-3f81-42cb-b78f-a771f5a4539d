"""
Integration tests for the attendance transfer script.
"""
import pytest
import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import pytz

# Add the parent directory to the path to import sqlscript
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlscript


class TestEnvironmentIntegration:
    """Test environment variable integration."""
    
    def test_env_file_loading(self, temp_env_file):
        """Test that .env file is properly loaded."""
        # Change to the directory containing the .env file
        env_dir = os.path.dirname(temp_env_file)
        original_cwd = os.getcwd()
        
        try:
            os.chdir(env_dir)
            
            # Reload the module to pick up the .env file
            import importlib
            importlib.reload(sqlscript)
            
            # Check that environment variables are loaded
            assert os.getenv('SOURCE_DB_SERVER') == 'test-server'
            assert os.getenv('SOURCE_DB_DATABASE') == 'test_db'
            assert os.getenv('ORANGEHRM_DB_HOST') == 'test-host'
            
        finally:
            os.chdir(original_cwd)
    
    def test_config_objects_use_env_vars(self, mock_env_vars):
        """Test that configuration objects use environment variables."""
        with patch.dict(os.environ, mock_env_vars):
            # Reload module to pick up new environment variables
            import importlib
            importlib.reload(sqlscript)
            
            # Check SOURCE_DB_CONFIG
            assert sqlscript.SOURCE_DB_CONFIG['server'] == 'test-server\\SQLSERVER'
            assert sqlscript.SOURCE_DB_CONFIG['database'] == 'test_att_db'
            assert sqlscript.SOURCE_DB_CONFIG['username'] == 'test_user'
            assert sqlscript.SOURCE_DB_CONFIG['password'] == 'test_password'
            
            # Check ORANGEHRM_DB_CONFIG
            assert sqlscript.ORANGEHRM_DB_CONFIG['host'] == 'test-mysql-host'
            assert sqlscript.ORANGEHRM_DB_CONFIG['port'] == 3306
            assert sqlscript.ORANGEHRM_DB_CONFIG['user'] == 'test_mysql_user'
            assert sqlscript.ORANGEHRM_DB_CONFIG['password'] == 'test_mysql_password'
            assert sqlscript.ORANGEHRM_DB_CONFIG['database'] == 'test_orangehrm_db'
            
            # Check DEFAULT_TIMEZONE
            assert sqlscript.DEFAULT_TIMEZONE == 'Pacific/Guadalcanal'


class TestEndToEndScenarios:
    """Test end-to-end scenarios."""
    
    @patch('sqlscript.schedule')
    @patch('sqlscript.transfer_attendance_data')
    @patch('sqlscript.check_required_env_vars')
    def test_main_execution_success(self, mock_check_env, mock_transfer, mock_schedule, mock_env_vars):
        """Test successful main execution flow."""
        mock_check_env.return_value = True
        
        with patch.dict(os.environ, mock_env_vars):
            with patch('sqlscript.time.sleep') as mock_sleep:
                # Mock schedule.run_pending to avoid infinite loop
                mock_schedule.run_pending.return_value = None
                mock_sleep.side_effect = KeyboardInterrupt  # Break the loop
                
                try:
                    # Import and reload to simulate main execution
                    import importlib
                    importlib.reload(sqlscript)
                    
                    # This would normally run the main block, but we'll call it directly
                    with pytest.raises(KeyboardInterrupt):
                        exec("""
if True:  # Simulate if __name__ == "__main__":
    if not sqlscript.check_required_env_vars():
        exit(1)
    
    sqlscript.transfer_attendance_data()
    sqlscript.schedule.every(5).minutes.do(sqlscript.transfer_attendance_data)
    
    while True:
        sqlscript.schedule.run_pending()
        sqlscript.time.sleep(1)
""")
                except SystemExit:
                    pass
                
                # Verify the flow
                mock_check_env.assert_called()
                mock_transfer.assert_called()
    
    @patch('sqlscript.check_required_env_vars')
    def test_main_execution_missing_env_vars(self, mock_check_env, caplog):
        """Test main execution with missing environment variables."""
        mock_check_env.return_value = False
        
        with pytest.raises(SystemExit) as exc_info:
            exec("""
if True:  # Simulate if __name__ == "__main__":
    if not sqlscript.check_required_env_vars():
        exit(1)
""")
        
        assert exc_info.value.code == 1
        mock_check_env.assert_called()


class TestDataFlowIntegration:
    """Test complete data flow integration."""
    
    @patch('sqlscript.mysql.connector.connect')
    @patch('sqlscript.pyodbc.connect')
    def test_complete_data_flow_punch_in_out(self, mock_pyodbc, mock_mysql, mock_env_vars):
        """Test complete data flow for punch in/out scenario."""
        # Setup mock connections
        sql_conn = Mock()
        sql_cursor = Mock()
        mysql_conn = Mock()
        mysql_cursor = Mock()
        
        mock_pyodbc.return_value = sql_conn
        mock_mysql.return_value = mysql_conn
        sql_conn.cursor.return_value = sql_cursor
        mysql_conn.cursor.return_value = mysql_cursor
        
        # Mock SQL Server data (punch in and punch out for same employee)
        sql_cursor.description = [('AttID',), ('employeeID',), ('checkTime',), ('checkType',)]
        sql_cursor.fetchall.return_value = [
            (1, 1001, datetime(2025, 7, 28, 8, 0, 0), 'IN'),
            (2, 1001, datetime(2025, 7, 28, 17, 0, 0), 'OUT')
        ]
        sql_cursor.fetchone.side_effect = [
            [datetime(2025, 7, 28, 15, 30, 0)],  # Server time
        ]
        
        # Mock MySQL responses
        mysql_cursor.fetchone.side_effect = [
            ('1001',),  # Employee exists for first record
            None,       # No duplicate for punch in
            ('1001',),  # Employee exists for second record  
            None,       # No duplicate for punch out
        ]
        mysql_cursor.rowcount = 1  # Successful update for punch out
        
        with patch.dict(os.environ, mock_env_vars):
            import importlib
            importlib.reload(sqlscript)
            
            # Execute the transfer
            sqlscript.transfer_attendance_data()
            
            # Verify connections were made
            mock_pyodbc.assert_called_once()
            mock_mysql.assert_called_once()
            
            # Verify data was processed
            assert sql_cursor.execute.called
            assert mysql_cursor.execute.called
            mysql_conn.commit.assert_called_once()
    
    @patch('sqlscript.mysql.connector.connect')
    @patch('sqlscript.pyodbc.connect')
    def test_complete_data_flow_with_errors(self, mock_pyodbc, mock_mysql, mock_env_vars, caplog):
        """Test complete data flow with various error conditions."""
        # Setup mock connections
        sql_conn = Mock()
        sql_cursor = Mock()
        mysql_conn = Mock()
        mysql_cursor = Mock()
        
        mock_pyodbc.return_value = sql_conn
        mock_mysql.return_value = mysql_conn
        sql_conn.cursor.return_value = sql_cursor
        mysql_conn.cursor.return_value = mysql_cursor
        
        # Mock SQL Server data with problematic records
        sql_cursor.description = [('AttID',), ('employeeID',), ('checkTime',), ('checkType',)]
        sql_cursor.fetchall.return_value = [
            (1, None, datetime(2025, 7, 28, 8, 0, 0), 'IN'),      # Null employee ID
            (2, 9999, datetime(2025, 7, 28, 9, 0, 0), 'IN'),      # Non-existent employee
            (3, 1001, datetime(2025, 7, 28, 10, 0, 0), 'INVALID') # Invalid check type
        ]
        sql_cursor.fetchone.side_effect = [
            [datetime(2025, 7, 28, 15, 30, 0)],  # Server time
        ]
        
        # Mock MySQL responses
        mysql_cursor.fetchone.side_effect = [
            None,       # Employee 9999 not found
        ]
        
        with patch.dict(os.environ, mock_env_vars):
            import importlib
            importlib.reload(sqlscript)
            
            # Execute the transfer
            sqlscript.transfer_attendance_data()
            
            # Verify error handling
            assert "Skipping record with null employeeID" in caplog.text
            assert "Employee 9999 not found in OrangeHRM" in caplog.text
            assert "Unknown checkType 'INVALID'" in caplog.text
            
            # Should still commit (even with errors)
            mysql_conn.commit.assert_called_once()


class TestConfigurationIntegration:
    """Test configuration integration."""
    
    def test_logging_configuration_from_env(self, mock_env_vars):
        """Test that logging configuration uses environment variables."""
        with patch.dict(os.environ, mock_env_vars):
            import importlib
            importlib.reload(sqlscript)
            
            # Check that logging level is set from environment
            import logging
            logger = logging.getLogger()
            # Note: The actual level check would depend on how logging is configured
            # This is more of a smoke test to ensure no errors occur
    
    def test_timezone_configuration_from_env(self, mock_env_vars):
        """Test that timezone configuration uses environment variables."""
        custom_env = mock_env_vars.copy()
        custom_env['DEFAULT_TIMEZONE'] = 'UTC'
        
        with patch.dict(os.environ, custom_env):
            import importlib
            importlib.reload(sqlscript)
            
            assert sqlscript.DEFAULT_TIMEZONE == 'UTC'
            
            # Test time conversion with the new timezone
            naive_dt = datetime(2025, 7, 28, 12, 0, 0)
            utc_dt, offset = sqlscript.convert_to_utc_and_offset(naive_dt)
            
            # UTC should have 0 offset
            assert offset == 0
            assert utc_dt == naive_dt
