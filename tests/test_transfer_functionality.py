"""
Tests for attendance data transfer functionality.
"""
import pytest
import os
import sys
from unittest.mock import Mo<PERSON>, patch, MagicMock, call
from datetime import datetime
import pytz

# Add the parent directory to the path to import sqlscript
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlscript


class TestTransferAttendanceData:
    """Test the main transfer_attendance_data function."""
    
    @patch('sqlscript.create_orangehrm_connection')
    @patch('sqlscript.create_source_connection')
    def test_transfer_attendance_data_no_connections(self, mock_source_conn, mock_orange_conn, caplog):
        """Test transfer when database connections fail."""
        mock_source_conn.return_value = None
        mock_orange_conn.return_value = None
        
        sqlscript.transfer_attendance_data()
        
        # Should return early without processing
        assert mock_source_conn.called
        assert mock_orange_conn.called
    
    @patch('sqlscript.get_attendance_records')
    @patch('sqlscript.create_orangehrm_connection')
    @patch('sqlscript.create_source_connection')
    def test_transfer_attendance_data_no_records(self, mock_source_conn, mock_orange_conn, 
                                                mock_get_records, caplog):
        """Test transfer when no attendance records are found."""
        # Mock successful connections
        mock_source_conn.return_value = Mock()
        mock_orange_conn.return_value = Mock()
        
        # Mock no records found
        mock_get_records.return_value = []
        
        sqlscript.transfer_attendance_data()
        
        assert "No attendance records found for today" in caplog.text
    
    @patch('sqlscript.convert_to_utc_and_offset')
    @patch('sqlscript.get_attendance_records')
    @patch('sqlscript.create_orangehrm_connection')
    @patch('sqlscript.create_source_connection')
    def test_transfer_attendance_data_punch_in_success(self, mock_source_conn, mock_orange_conn,
                                                      mock_get_records, mock_convert_time, caplog):
        """Test successful punch-in record transfer."""
        # Mock connections
        source_conn = Mock()
        orange_conn = Mock()
        orange_cursor = Mock()
        
        mock_source_conn.return_value = source_conn
        mock_orange_conn.return_value = orange_conn
        orange_conn.cursor.return_value = orange_cursor
        
        # Mock attendance records
        mock_get_records.return_value = [
            {
                'AttID': 1,
                'employeeID': 1001,
                'checkTime': datetime(2025, 7, 28, 8, 0, 0),
                'checkType': 'IN'
            }
        ]
        
        # Mock time conversion
        mock_convert_time.return_value = (datetime(2025, 7, 27, 21, 0, 0), 660)
        
        # Mock employee exists check
        orange_cursor.fetchone.side_effect = [
            ('1001',),  # Employee exists
            None        # No duplicate record
        ]
        
        sqlscript.transfer_attendance_data()
        
        # Verify the insert query was called
        assert orange_cursor.execute.call_count >= 2  # Employee check + insert
        orange_conn.commit.assert_called_once()
        assert "Transfer complete!" in caplog.text
    
    @patch('sqlscript.convert_to_utc_and_offset')
    @patch('sqlscript.get_attendance_records')
    @patch('sqlscript.create_orangehrm_connection')
    @patch('sqlscript.create_source_connection')
    def test_transfer_attendance_data_punch_out_success(self, mock_source_conn, mock_orange_conn,
                                                       mock_get_records, mock_convert_time, caplog):
        """Test successful punch-out record transfer."""
        # Mock connections
        source_conn = Mock()
        orange_conn = Mock()
        orange_cursor = Mock()
        
        mock_source_conn.return_value = source_conn
        mock_orange_conn.return_value = orange_conn
        orange_conn.cursor.return_value = orange_cursor
        
        # Mock attendance records
        mock_get_records.return_value = [
            {
                'AttID': 2,
                'employeeID': 1001,
                'checkTime': datetime(2025, 7, 28, 17, 0, 0),
                'checkType': 'OUT'
            }
        ]
        
        # Mock time conversion
        mock_convert_time.return_value = (datetime(2025, 7, 28, 6, 0, 0), 660)
        
        # Mock employee exists and no duplicate
        orange_cursor.fetchone.side_effect = [
            ('1001',),  # Employee exists
            None        # No duplicate record
        ]
        
        # Mock successful update (rowcount > 0)
        orange_cursor.rowcount = 1
        
        sqlscript.transfer_attendance_data()
        
        # Verify update query was called
        assert orange_cursor.execute.call_count >= 2
        orange_conn.commit.assert_called_once()
        assert "Transfer complete!" in caplog.text
    
    @patch('sqlscript.convert_to_utc_and_offset')
    @patch('sqlscript.get_attendance_records')
    @patch('sqlscript.create_orangehrm_connection')
    @patch('sqlscript.create_source_connection')
    def test_transfer_attendance_data_employee_not_found(self, mock_source_conn, mock_orange_conn,
                                                        mock_get_records, mock_convert_time, caplog):
        """Test behavior when employee is not found in OrangeHRM."""
        # Mock connections
        source_conn = Mock()
        orange_conn = Mock()
        orange_cursor = Mock()
        
        mock_source_conn.return_value = source_conn
        mock_orange_conn.return_value = orange_conn
        orange_conn.cursor.return_value = orange_cursor
        
        # Mock attendance records
        mock_get_records.return_value = [
            {
                'AttID': 1,
                'employeeID': 9999,  # Non-existent employee
                'checkTime': datetime(2025, 7, 28, 8, 0, 0),
                'checkType': 'IN'
            }
        ]
        
        # Mock time conversion
        mock_convert_time.return_value = (datetime(2025, 7, 27, 21, 0, 0), 660)
        
        # Mock employee not found
        orange_cursor.fetchone.return_value = None
        
        sqlscript.transfer_attendance_data()
        
        assert "Employee 9999 not found in OrangeHRM" in caplog.text
        orange_conn.commit.assert_called_once()
    
    @patch('sqlscript.convert_to_utc_and_offset')
    @patch('sqlscript.get_attendance_records')
    @patch('sqlscript.create_orangehrm_connection')
    @patch('sqlscript.create_source_connection')
    def test_transfer_attendance_data_duplicate_record(self, mock_source_conn, mock_orange_conn,
                                                      mock_get_records, mock_convert_time, caplog):
        """Test behavior when duplicate record is found."""
        # Mock connections
        source_conn = Mock()
        orange_conn = Mock()
        orange_cursor = Mock()
        
        mock_source_conn.return_value = source_conn
        mock_orange_conn.return_value = orange_conn
        orange_conn.cursor.return_value = orange_cursor
        
        # Mock attendance records
        mock_get_records.return_value = [
            {
                'AttID': 1,
                'employeeID': 1001,
                'checkTime': datetime(2025, 7, 28, 8, 0, 0),
                'checkType': 'IN'
            }
        ]
        
        # Mock time conversion
        mock_convert_time.return_value = (datetime(2025, 7, 27, 21, 0, 0), 660)
        
        # Mock employee exists and duplicate found
        orange_cursor.fetchone.side_effect = [
            ('1001',),      # Employee exists
            ('existing',)   # Duplicate record found
        ]
        
        sqlscript.transfer_attendance_data()
        
        # Should skip the duplicate
        assert "Transfer complete! Transferred: 0, Duplicates: 1, Errors: 0" in caplog.text
    
    @patch('sqlscript.convert_to_utc_and_offset')
    @patch('sqlscript.get_attendance_records')
    @patch('sqlscript.create_orangehrm_connection')
    @patch('sqlscript.create_source_connection')
    def test_transfer_attendance_data_null_employee_id(self, mock_source_conn, mock_orange_conn,
                                                      mock_get_records, mock_convert_time, caplog):
        """Test behavior when employeeID is null."""
        # Mock connections
        source_conn = Mock()
        orange_conn = Mock()
        orange_cursor = Mock()
        
        mock_source_conn.return_value = source_conn
        mock_orange_conn.return_value = orange_conn
        orange_conn.cursor.return_value = orange_cursor
        
        # Mock attendance records with null employeeID
        mock_get_records.return_value = [
            {
                'AttID': 1,
                'employeeID': None,  # Null employee ID
                'checkTime': datetime(2025, 7, 28, 8, 0, 0),
                'checkType': 'IN'
            }
        ]
        
        sqlscript.transfer_attendance_data()
        
        assert "Skipping record with null employeeID" in caplog.text
    
    @patch('sqlscript.convert_to_utc_and_offset')
    @patch('sqlscript.get_attendance_records')
    @patch('sqlscript.create_orangehrm_connection')
    @patch('sqlscript.create_source_connection')
    def test_transfer_attendance_data_unknown_check_type(self, mock_source_conn, mock_orange_conn,
                                                        mock_get_records, mock_convert_time, caplog):
        """Test behavior with unknown checkType."""
        # Mock connections
        source_conn = Mock()
        orange_conn = Mock()
        orange_cursor = Mock()
        
        mock_source_conn.return_value = source_conn
        mock_orange_conn.return_value = orange_conn
        orange_conn.cursor.return_value = orange_cursor
        
        # Mock attendance records
        mock_get_records.return_value = [
            {
                'AttID': 1,
                'employeeID': 1001,
                'checkTime': datetime(2025, 7, 28, 8, 0, 0),
                'checkType': 'UNKNOWN'  # Unknown check type
            }
        ]
        
        # Mock time conversion
        mock_convert_time.return_value = (datetime(2025, 7, 27, 21, 0, 0), 660)
        
        # Mock employee exists
        orange_cursor.fetchone.return_value = ('1001',)
        
        sqlscript.transfer_attendance_data()
        
        assert "Unknown checkType 'UNKNOWN'" in caplog.text
        assert "Transfer complete! Transferred: 0, Duplicates: 0, Errors: 1" in caplog.text
