"""
Tests for sqlscript.py - Attendance Transfer Script
"""
import pytest
import os
import sys
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import pytz

# Add the parent directory to the path to import sqlscript
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlscript


class TestEnvironmentVariables:
    """Test environment variable handling."""
    
    def test_check_required_env_vars_all_present(self, mock_env_vars):
        """Test that check_required_env_vars returns True when all vars are present."""
        with patch.dict(os.environ, mock_env_vars):
            assert sqlscript.check_required_env_vars() is True
    
    def test_check_required_env_vars_missing_vars(self, caplog):
        """Test that check_required_env_vars returns False when vars are missing."""
        # Clear all environment variables
        with patch.dict(os.environ, {}, clear=True):
            result = sqlscript.check_required_env_vars()
            assert result is False
            assert "Missing required environment variables" in caplog.text
    
    def test_check_required_env_vars_partial_missing(self, mock_env_vars, caplog):
        """Test behavior when only some required vars are missing."""
        # Remove a few required variables
        incomplete_vars = mock_env_vars.copy()
        del incomplete_vars['SOURCE_DB_PASSWORD']
        del incomplete_vars['ORANGEHRM_DB_HOST']
        
        with patch.dict(os.environ, incomplete_vars, clear=True):
            result = sqlscript.check_required_env_vars()
            assert result is False
            assert "SOURCE_DB_PASSWORD" in caplog.text
            assert "ORANGEHRM_DB_HOST" in caplog.text


class TestDatabaseConnections:
    """Test database connection functions."""
    
    @patch('sqlscript.pyodbc.connect')
    def test_create_source_connection_success(self, mock_connect, mock_env_vars):
        """Test successful SQL Server connection."""
        mock_connection = Mock()
        mock_connect.return_value = mock_connection
        
        with patch.dict(os.environ, mock_env_vars):
            # Reload the module to pick up new env vars
            import importlib
            importlib.reload(sqlscript)
            
            result = sqlscript.create_source_connection()
            assert result == mock_connection
            mock_connect.assert_called_once()
    
    @patch('sqlscript.pyodbc.connect')
    def test_create_source_connection_failure(self, mock_connect, mock_env_vars, caplog):
        """Test SQL Server connection failure."""
        import pyodbc
        mock_connect.side_effect = pyodbc.Error("Connection failed")

        with patch.dict(os.environ, mock_env_vars):
            import importlib
            importlib.reload(sqlscript)

            result = sqlscript.create_source_connection()
            assert result is None
            assert "Error connecting to SQL Server" in caplog.text
    
    @patch('sqlscript.mysql.connector.connect')
    def test_create_orangehrm_connection_success(self, mock_connect, mock_env_vars):
        """Test successful MySQL connection."""
        mock_connection = Mock()
        mock_connect.return_value = mock_connection
        
        with patch.dict(os.environ, mock_env_vars):
            import importlib
            importlib.reload(sqlscript)
            
            result = sqlscript.create_orangehrm_connection()
            assert result == mock_connection
            mock_connect.assert_called_once()
    
    @patch('sqlscript.mysql.connector.connect')
    def test_create_orangehrm_connection_failure(self, mock_connect, mock_env_vars, caplog):
        """Test MySQL connection failure."""
        from mysql.connector import Error
        mock_connect.side_effect = Error("MySQL connection failed")
        
        with patch.dict(os.environ, mock_env_vars):
            import importlib
            importlib.reload(sqlscript)
            
            result = sqlscript.create_orangehrm_connection()
            assert result is None
            assert "Error connecting to MySQL" in caplog.text


class TestTimeConversion:
    """Test timezone and time conversion functions."""
    
    def test_convert_to_utc_and_offset_valid_timezone(self):
        """Test time conversion with valid timezone."""
        # Test with Pacific/Guadalcanal timezone
        naive_dt = datetime(2025, 7, 28, 10, 30, 0)
        
        with patch('sqlscript.DEFAULT_TIMEZONE', 'Pacific/Guadalcanal'):
            utc_dt, offset = sqlscript.convert_to_utc_and_offset(naive_dt)
            
            assert isinstance(utc_dt, datetime)
            assert utc_dt.tzinfo is None  # Should be naive UTC
            assert isinstance(offset, int)
            # Pacific/Guadalcanal is UTC+11, so offset should be 660 minutes
            assert offset == 660
    
    def test_convert_to_utc_and_offset_invalid_timezone(self, caplog):
        """Test time conversion with invalid timezone."""
        naive_dt = datetime(2025, 7, 28, 10, 30, 0)
        
        with patch('sqlscript.DEFAULT_TIMEZONE', 'Invalid/Timezone'):
            utc_dt, offset = sqlscript.convert_to_utc_and_offset(naive_dt)
            
            # Should fallback to original datetime and 0 offset
            assert utc_dt == naive_dt
            assert offset == 0
            assert "Invalid default timezone" in caplog.text
    
    def test_convert_to_utc_and_offset_with_timezone_aware(self):
        """Test time conversion with timezone-aware datetime."""
        tz = pytz.timezone('Pacific/Guadalcanal')
        aware_dt = tz.localize(datetime(2025, 7, 28, 10, 30, 0))
        
        with patch('sqlscript.DEFAULT_TIMEZONE', 'Pacific/Guadalcanal'):
            utc_dt, offset = sqlscript.convert_to_utc_and_offset(aware_dt)
            
            assert isinstance(utc_dt, datetime)
            assert utc_dt.tzinfo is None
            assert offset == 660


class TestAttendanceRecords:
    """Test attendance record processing."""
    
    @patch('sqlscript.datetime')
    def test_get_attendance_records_success(self, mock_datetime, mock_sql_connection, sample_attendance_records):
        """Test successful retrieval of attendance records."""
        mock_conn, mock_cursor = mock_sql_connection
        
        # Mock datetime.now to return a fixed time
        fixed_now = datetime(2025, 7, 28, 15, 30, 0)
        mock_datetime.now.return_value = fixed_now
        mock_datetime.combine = datetime.combine
        mock_datetime.min = datetime.min
        
        # Mock cursor.fetchall to return sample records
        mock_cursor.fetchall.return_value = [
            (1, 1001, datetime(2025, 7, 28, 8, 0, 0), 'IN'),
            (2, 1001, datetime(2025, 7, 28, 17, 0, 0), 'OUT')
        ]
        
        # Mock server time query
        mock_cursor.fetchone.return_value = [fixed_now]
        
        with patch('sqlscript.DEFAULT_TIMEZONE', 'Pacific/Guadalcanal'):
            records = sqlscript.get_attendance_records(mock_conn)
            
            assert len(records) == 2
            assert records[0]['employeeID'] == 1001
            assert records[0]['checkType'] == 'IN'
            assert records[1]['checkType'] == 'OUT'
    
    def test_get_attendance_records_no_records(self, mock_sql_connection, caplog):
        """Test behavior when no attendance records are found."""
        mock_conn, mock_cursor = mock_sql_connection
        
        # Mock empty result
        mock_cursor.fetchall.return_value = []
        mock_cursor.fetchone.side_effect = [
            [datetime.now()],  # Server time
            None,  # No sample record
            [0]    # Count query
        ]
        
        with patch('sqlscript.DEFAULT_TIMEZONE', 'UTC'):
            records = sqlscript.get_attendance_records(mock_conn)
            
            assert records == []
            assert "No records found in dbo.att table at all" in caplog.text
    
    def test_get_attendance_records_exception(self, mock_sql_connection, caplog):
        """Test exception handling in get_attendance_records."""
        mock_conn, mock_cursor = mock_sql_connection
        
        # Mock exception during query execution
        mock_cursor.execute.side_effect = Exception("Database error")
        
        records = sqlscript.get_attendance_records(mock_conn)
        
        assert records == []
        assert "Error fetching attendance records" in caplog.text
