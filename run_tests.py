#!/usr/bin/env python3
"""
Test runner script for the attendance transfer application.
"""
import sys
import subprocess
import os


def run_tests(test_type="all", verbose=False):
    """
    Run tests with different configurations.
    
    Args:
        test_type (str): Type of tests to run ('unit', 'integration', 'all')
        verbose (bool): Whether to run in verbose mode
    """
    base_cmd = ["python", "-m", "pytest"]
    
    if verbose:
        base_cmd.append("-v")
    
    if test_type == "unit":
        base_cmd.extend(["-m", "unit"])
    elif test_type == "integration":
        base_cmd.extend(["-m", "integration"])
    elif test_type == "all":
        pass  # Run all tests
    else:
        print(f"Unknown test type: {test_type}")
        return 1
    
    print(f"Running {test_type} tests...")
    print(f"Command: {' '.join(base_cmd)}")
    
    try:
        result = subprocess.run(base_cmd, check=False)
        return result.returncode
    except FileNotFoundError:
        print("Error: pytest not found. Please install it with: pip install pytest")
        return 1
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 1


def main():
    """Main function to handle command line arguments."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run tests for attendance transfer script")
    parser.add_argument(
        "--type", 
        choices=["unit", "integration", "all"], 
        default="all",
        help="Type of tests to run (default: all)"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests in verbose mode"
    )
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Run tests with coverage report"
    )
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install test dependencies before running tests"
    )
    
    args = parser.parse_args()
    
    # Install dependencies if requested
    if args.install_deps:
        print("Installing test dependencies...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
            print("Dependencies installed successfully.")
        except subprocess.CalledProcessError:
            print("Error installing dependencies")
            return 1
    
    # Check if pytest is available
    try:
        subprocess.run([sys.executable, "-m", "pytest", "--version"], 
                      capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("pytest not found. Installing...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pytest", "pytest-mock", "pytest-cov"], 
                          check=True)
        except subprocess.CalledProcessError:
            print("Error installing pytest")
            return 1
    
    return run_tests(args.type, args.verbose)


if __name__ == "__main__":
    sys.exit(main())
