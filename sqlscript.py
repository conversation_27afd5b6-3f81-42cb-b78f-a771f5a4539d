import schedule
import time
import mysql.connector
from mysql.connector import <PERSON>rro<PERSON>
from datetime import datetime, timedelta
import pytz
import logging
import pyodbc
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Check for required environment variables
def check_required_env_vars():
    required_vars = [
        'SOURCE_DB_SERVER', 'SOURCE_DB_DATABASE', 'SOURCE_DB_USERNAME', 'SOURCE_DB_PASSWORD',
        'ORANGEHRM_DB_HOST', 'ORANGEHRM_DB_USER', 'ORANGEHRM_DB_PASSWORD', 'ORANGEHRM_DB_DATABASE'
    ]
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logging.error("Please create a .env file with the required variables (see .env.example)")
        return False
    return True

# Configure logging
log_level = getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper())
log_file = os.getenv('LOG_FILE', 'attendance_transfer.log')

logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

# Database configuration using environment variables
SOURCE_DB_CONFIG = {
    'Driver': os.getenv('SOURCE_DB_DRIVER', 'ODBC Driver 17 for SQL Server'),
    'server': os.getenv('SOURCE_DB_SERVER'),
    'database': os.getenv('SOURCE_DB_DATABASE'),
    'username': os.getenv('SOURCE_DB_USERNAME'),
    'password': os.getenv('SOURCE_DB_PASSWORD'),
    'trusted_connetion': os.getenv('SOURCE_DB_TRUSTED_CONNECTION', 'yes'),
    'encrypt': os.getenv('SOURCE_DB_ENCRYPT', 'false')
}

ORANGEHRM_DB_CONFIG = {
    'host': os.getenv('ORANGEHRM_DB_HOST'),
    'port': int(os.getenv('ORANGEHRM_DB_PORT', '3306')),
    'user': os.getenv('ORANGEHRM_DB_USER'),
    'password': os.getenv('ORANGEHRM_DB_PASSWORD'),
    'database': os.getenv('ORANGEHRM_DB_DATABASE')
}

# Default timezone for all records
DEFAULT_TIMEZONE = os.getenv('DEFAULT_TIMEZONE', 'Pacific/Guadalcanal')

def create_source_connection():
    """Create SQL Server connection using pyodbc"""
    try:
        conn_str = f"SERVER={SOURCE_DB_CONFIG['server']};" \
                   f"DRIVER={SOURCE_DB_CONFIG['Driver']};" \
                   f"DATABASE={SOURCE_DB_CONFIG['database']};" \
                   f"USERNAME={SOURCE_DB_CONFIG['username']};" \
                   f"PASSWORD={SOURCE_DB_CONFIG['password']};" \
                   f"Trusted_Connection={SOURCE_DB_CONFIG['trusted_connetion']};" \
                   f"Trusted_Connection={SOURCE_DB_CONFIG['encrypt']};"

        conn = pyodbc.connect(conn_str)
        logging.info("SQL Server connection successful")
        return conn
    except pyodbc.Error as e:
        logging.error(f"Error connecting to SQL Server: {e}")
        return None

def create_orangehrm_connection():
    """Create MySQL connection for OrangeHRM"""
    try:
        conn = mysql.connector.connect(
            host=ORANGEHRM_DB_CONFIG['host'],
            port=ORANGEHRM_DB_CONFIG['port'],
            user=ORANGEHRM_DB_CONFIG['user'],
            password=ORANGEHRM_DB_CONFIG['password'],
            database=ORANGEHRM_DB_CONFIG['database'],
        )
        logging.info("MySQL connection successful")
        return conn
    except Error as e:
        logging.error(f"Error connecting to MySQL: {e}")
        return None

def get_attendance_records(conn):
    """Fetch today's attendance records without pairing"""
    try:
        # Get today's date in the default timezone
        tz = pytz.timezone(DEFAULT_TIMEZONE)
        now_in_tz = datetime.now(tz)
        today = now_in_tz.date()

        # Calculate start and end of day in the default timezone
        start_of_day = datetime.combine(today, datetime.min.time())
        end_of_day = start_of_day + timedelta(days=1)

        # Format for SQL query (using local time)
        start_str = start_of_day.strftime('%Y-%m-%d %H:%M:%S')
        end_str = end_of_day.strftime('%Y-%m-%d %H:%M:%S')

        logging.info(f"Fetching records for {today} (Local range: {start_str} to {end_str})")

        # Debug: Check server time
        cursor = conn.cursor()
        cursor.execute("SELECT GETDATE()")
        server_time = cursor.fetchone()[0]
        logging.info(f"SQL Server current time: {server_time}")

        query = """
            SELECT AttID, employeeID, checkTime, checkType
            FROM dbo.attendance
            WHERE checkTime >= ? AND checkTime < ?
            ORDER BY checkTime
        """

        cursor.execute(query, (start_of_day, end_of_day))
        columns = [column[0] for column in cursor.description]
        records = [dict(zip(columns, row)) for row in cursor.fetchall()]
        logging.info(f"Fetched {len(records)} attendance records for today")

        # Log first 5 records if any exist
        if records:
            for i, rec in enumerate(records[:min(5, len(records))]):
                logging.info(f"Record {i + 1}: ID={rec['AttID']}, EmpID={rec['employeeID']}, "
                             f"Time={rec['checkTime']}, Type={rec['checkType']}")
        else:
            # Debug: Check if any records exist at all
            cursor.execute("SELECT TOP 1 * FROM dbo.attendance ORDER BY AttID DESC")
            sample = cursor.fetchone()
            if sample:
                logging.info(f"Sample record exists: ID={sample[0]}, Time={sample[2]}")
            else:
                logging.warning("No records found in dbo.att table at all")

            # Debug: Check count in date range
            cursor.execute("SELECT COUNT(*) FROM dbo.attendance WHERE checkTime >= ? AND checkTime < ?",
                           (start_of_day, end_of_day))
            count = cursor.fetchone()[0]
            logging.info(f"Count of records in date range: {count}")

        return records

    except Exception as e:
        logging.error(f"Error fetching attendance records: {e}", exc_info=True)
        return []
    finally:
        cursor.close()

def convert_to_utc_and_offset(naive_dt):
    """Convert naive datetime to UTC and calculate offset using DEFAULT_TIMEZONE"""
    try:
        tz = pytz.timezone(DEFAULT_TIMEZONE)
    except pytz.UnknownTimeZoneError:
        logging.error(f"Invalid default timezone: {DEFAULT_TIMEZONE}. Using UTC")
        tz = pytz.UTC

    try:
        # Ensure naive_dt is timezone-naive
        if naive_dt.tzinfo is not None:
            naive_dt = naive_dt.replace(tzinfo=None)

        localized_dt = tz.localize(naive_dt)
        utc_dt = localized_dt.astimezone(pytz.utc)
        offset = localized_dt.utcoffset().total_seconds() / 60
        return utc_dt.replace(tzinfo=None), int(offset)
    except Exception as e:
        logging.error(f"Time conversion error: {e}")
        return naive_dt, 0  # Fallback


def transfer_attendance_data():
    """Main function to transfer attendance data"""
    source_conn = create_source_connection()
    orange_conn = create_orangehrm_connection()

    if not source_conn or not orange_conn:
        return

    attendance_records = get_attendance_records(source_conn)
    if not attendance_records:
        logging.info("No attendance records found for today")
        return

    transferred = 0
    duplicates = 0
    errors = 0

    insert_query = """
                   INSERT INTO ohrm_attendance_record (employee_id, \
                                                       punch_in_user_time, punch_in_utc_time, punch_in_time_offset, \
                                                       punch_in_timezone_name, \
                                                       punch_out_user_time, punch_out_utc_time, punch_out_time_offset, \
                                                       punch_out_timezone_name, \
                                                       state, punch_in_note, punch_out_note) \
                   VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) \
                   """

    try:
        orange_cursor = orange_conn.cursor()

        for record in attendance_records:
            try:
                # Skip records with null employeeID
                if record['employeeID'] is None:
                    logging.warning(f"Skipping record with null employeeID: ID={record['AttID']}")
                    continue

                # Format employeeID as 4-digit string
                emp_id = str(record['employeeID']).zfill(4)

                # Validate employee exists in OrangeHRM
                emp_check = "SELECT employee_id FROM hs_hr_employee WHERE employee_id = %s"
                orange_cursor.execute(emp_check, (emp_id,))
                if not orange_cursor.fetchone():
                    logging.warning(f"Employee {emp_id} not found in OrangeHRM")
                    continue

                # Convert time to UTC and offset using DEFAULT_TIMEZONE
                utc_time, offset = convert_to_utc_and_offset(record['checkTime'])

                # Convert datetime objects to string in ISO format
                user_time_str = record['checkTime'].strftime('%Y-%m-%d %H:%M:%S')
                utc_time_str = utc_time.strftime('%Y-%m-%d %H:%M:%S')

                # Process based on checkType
                check_type = str(record['checkType']).strip().upper()
                if check_type in ['IN', 'I']:
                    # PUNCH-IN: Check for duplicate
                    dup_check = """
                                SELECT 1 \
                                FROM ohrm_attendance_record
                                WHERE employee_id = %s \
                                  AND punch_in_utc_time = %s \
                                """
                    orange_cursor.execute(dup_check, (emp_id, utc_time_str))
                    if orange_cursor.fetchone():
                        duplicates += 1
                        continue

                    # Insert new punch-in record
                    data = (
                        emp_id,
                        user_time_str,  # punch_in_user_time
                        utc_time_str,  # punch_in_utc_time
                        offset,  # punch_in_time_offset
                        DEFAULT_TIMEZONE,  # punch_in_timezone_name
                        None, None, None, None,  # punch-out fields
                        'PUNCHED IN', 'Biometric System', ''  # state and notes
                    )
                    orange_cursor.execute(insert_query, data)
                    transferred += 1

                elif check_type in ['OUT', 'O']:
                    # PUNCH-OUT: Check for duplicate
                    dup_check = """
                                SELECT 1 \
                                FROM ohrm_attendance_record
                                WHERE employee_id = %s \
                                  AND punch_out_utc_time = %s \
                                """
                    orange_cursor.execute(dup_check, (emp_id, utc_time_str))
                    if orange_cursor.fetchone():
                        duplicates += 1
                        continue

                    # Try to update most recent matching punch-in record
                    update_query = """
                                   UPDATE ohrm_attendance_record
                                   SET punch_out_user_time     = %s,
                                       punch_out_utc_time      = %s,
                                       punch_out_time_offset   = %s,
                                       punch_out_timezone_name = %s,
                                       state                   = 'PUNCHED OUT',
                                       punch_out_note          = %s
                                   WHERE employee_id = %s
                                     AND punch_out_utc_time IS NULL
                                     AND punch_in_utc_time < %s ORDER BY punch_in_utc_time DESC
                        LIMIT 1 \
                                   """
                    update_data = (
                        user_time_str,  # punch_out_user_time
                        utc_time_str,  # punch_out_utc_time
                        offset,  # punch_out_time_offset
                        DEFAULT_TIMEZONE,  # punch_out_timezone_name
                        'Biometric System',  # punch_out_note
                        emp_id,
                        utc_time_str  # For time comparison
                    )
                    orange_cursor.execute(update_query, update_data)

                    if orange_cursor.rowcount > 0:
                        # Successfully updated existing record
                        transferred += 1
                    else:
                        # No matching punch-in found - create new record
                        data = (
                            emp_id,
                            None, None, None, None,  # punch-in fields
                            user_time_str, utc_time_str, offset, DEFAULT_TIMEZONE,
                            'PUNCHED OUT', '', 'Biometric System'
                        )
                        orange_cursor.execute(insert_query, data)
                        transferred += 1

                else:
                    logging.warning(f"Unknown checkType '{check_type}' for record ID={record['id']}")
                    errors += 1

            except Exception as e:
                errors += 1
                logging.error(f"Error processing record ID={record.get('id')}: {e}")
                logging.error(f"Record details: {record}")

        orange_conn.commit()
        logging.info(f"Transfer complete! Transferred: {transferred}, Duplicates: {duplicates}, Errors: {errors}")

    except Exception as e:
        logging.error(f"Database error during transfer: {e}")
        if orange_conn:
            orange_conn.rollback()
    finally:
        if source_conn:
            source_conn.close()
        if orange_conn:
            if orange_cursor:
                orange_cursor.close()
            orange_conn.close()

if __name__ == "__main__":
    # Check for required environment variables before starting
    if not check_required_env_vars():
        logging.error("Cannot start application due to missing environment variables")
        exit(1)

    logging.info("Starting attendance transfer scheduler (runs every 5 minutes)")
    logging.info(f"Using timezone: {DEFAULT_TIMEZONE}")
    logging.info(f"Source DB: {SOURCE_DB_CONFIG['server']}/{SOURCE_DB_CONFIG['database']}")
    logging.info(f"Target DB: {ORANGEHRM_DB_CONFIG['host']}/{ORANGEHRM_DB_CONFIG['database']}")

    # Run immediately on startup
    transfer_attendance_data()

    # Schedule recurring job
    schedule.every(5).minutes.do(transfer_attendance_data)

    # Keep the script running
    while True:
        schedule.run_pending()
        time.sleep(1)